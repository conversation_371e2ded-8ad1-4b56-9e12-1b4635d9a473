import AppKit
import Foundation

class StandardPositionCalculation: WindowCalculation {
    override func calculateRect(_ params: RectCalculationParameters) -> RectResult {
        // Get the visible frame of the screen, ensuring we're using the correct coordinates
        let visibleFrameOfScreen = params.visibleFrameOfScreen

        // Log the frame information for debugging
        LoggingService.shared.debug(
            "Visible frame: \(params.visibleFrameOfScreen)", service: "StandardPositionCalculation")
        LoggingService.shared.debug(
            "Visible frame: \(visibleFrameOfScreen)", service: "StandardPositionCalculation")

        // Log the window information
        LoggingService.shared.debug(
            "Window frame: \(params.window.rect)", service: "StandardPositionCalculation")

        // Check if this is a secondary display
        let isSecondary =
            NSScreen.screens.first { $0.frame.contains(visibleFrameOfScreen.center) }
            != NSScreen.main
        LoggingService.shared.debug(
            "Is secondary display: \(isSecondary)", service: "StandardPositionCalculation")

        // If this is a secondary display, adjust the calculations
        if isSecondary {
            // Get the screen's scale factor
            let screenScale =
                NSScreen.screens.first { $0.frame.contains(visibleFrameOfScreen.center) }?
                .backingScaleFactor ?? 1.0
            let mainScale = NSScreen.main?.backingScaleFactor ?? 1.0

            // Log scale information
            LoggingService.shared.debug(
                "Screen scale factor: \(screenScale)", service: "StandardPositionCalculation")
            LoggingService.shared.debug(
                "Main screen scale factor: \(mainScale)", service: "StandardPositionCalculation")

            // Adjust the visible frame for secondary displays
            // This is necessary because the coordinate system may be different
            let screen = NSScreen.screens.first { $0.frame.contains(visibleFrameOfScreen.center) }!

            // Get the screen's frame and visible frame
            let screenFrame = screen.frame
            let screenVisibleFrame = screen.visibleFrame

            // Log the frame information
            LoggingService.shared.debug(
                "Screen frame: \(screenFrame)", service: "StandardPositionCalculation")
            LoggingService.shared.debug(
                "Screen visible frame: \(screenVisibleFrame)",
                service: "StandardPositionCalculation")
        }

        switch params.action {
        case .leftHalf:
            // Match Rectangle's implementation exactly
            var rect = visibleFrameOfScreen
            rect.size.width = floor(visibleFrameOfScreen.width / 2.0)
            return RectResult(rect)

        case .rightHalf:
            // Match Rectangle's implementation exactly
            var rect = visibleFrameOfScreen
            rect.size.width = floor(visibleFrameOfScreen.width / 2.0)
            rect.origin.x = visibleFrameOfScreen.maxX - rect.width
            return RectResult(rect)

        case .topHalf:
            // Match Rectangle's implementation exactly
            var rect = visibleFrameOfScreen
            rect.size.height = floor(visibleFrameOfScreen.height / 2.0)
            rect.origin.y = visibleFrameOfScreen.maxY - rect.height
            return RectResult(rect)

        case .bottomHalf:
            // Match Rectangle's implementation exactly
            var rect = visibleFrameOfScreen
            rect.size.height = floor(visibleFrameOfScreen.height / 2.0)

            // Log detailed information about the calculation
            LoggingService.shared.debug(
                "Bottom half calculation in StandardPositionCalculation",
                service: "StandardPositionCalculation"
            )
            LoggingService.shared.debug(
                "visibleFrameOfScreen: \(visibleFrameOfScreen)",
                service: "StandardPositionCalculation"
            )
            LoggingService.shared.debug(
                "Calculated rect: \(rect)",
                service: "StandardPositionCalculation"
            )

            return RectResult(rect)

        case .topLeft:
            // Match Rectangle's implementation exactly
            var rect = visibleFrameOfScreen
            rect.size.width = floor(visibleFrameOfScreen.width / 2.0)
            rect.size.height = floor(visibleFrameOfScreen.height / 2.0)
            rect.origin.y = visibleFrameOfScreen.maxY - rect.height
            return RectResult(rect)

        case .topRight:
            // Match Rectangle's implementation exactly
            var rect = visibleFrameOfScreen
            rect.size.width = floor(visibleFrameOfScreen.width / 2.0)
            rect.origin.x = visibleFrameOfScreen.maxX - rect.width
            rect.size.height = floor(visibleFrameOfScreen.height / 2.0)
            rect.origin.y = visibleFrameOfScreen.maxY - rect.height
            return RectResult(rect)

        case .bottomLeft:
            // Match Rectangle's implementation exactly
            var rect = visibleFrameOfScreen
            rect.size.width = floor(visibleFrameOfScreen.width / 2.0)
            rect.size.height = floor(visibleFrameOfScreen.height / 2.0)

            LoggingService.shared.debug(
                "Bottom left calculation using Rectangle's approach",
                service: "StandardPositionCalculation"
            )
            LoggingService.shared.debug(
                "Resulting rect: \(rect)",
                service: "StandardPositionCalculation"
            )

            return RectResult(rect)

        case .bottomRight:
            // Match Rectangle's implementation exactly
            var rect = visibleFrameOfScreen
            rect.size.width = floor(visibleFrameOfScreen.width / 2.0)
            rect.origin.x = visibleFrameOfScreen.maxX - rect.width
            rect.size.height = floor(visibleFrameOfScreen.height / 2.0)

            LoggingService.shared.debug(
                "Bottom right calculation using Rectangle's approach",
                service: "StandardPositionCalculation"
            )
            LoggingService.shared.debug(
                "Resulting rect: \(rect)",
                service: "StandardPositionCalculation"
            )

            return RectResult(rect)

        case .maximize:
            return RectResult(visibleFrameOfScreen)

        case .center:
            let windowRect = params.window.rect
            return RectResult(
                CGRect(
                    x: visibleFrameOfScreen.minX + (visibleFrameOfScreen.width - windowRect.width)
                        / 2,
                    y: visibleFrameOfScreen.minY + (visibleFrameOfScreen.height - windowRect.height)
                        / 2,
                    width: windowRect.width,
                    height: windowRect.height
                ))

        case .topLeftQuarter:
            return RectResult(
                CGRect(
                    x: visibleFrameOfScreen.minX,
                    y: visibleFrameOfScreen.minY + visibleFrameOfScreen.height / 2,
                    width: visibleFrameOfScreen.width / 2,
                    height: visibleFrameOfScreen.height / 2
                ))

        case .topRightQuarter:
            return RectResult(
                CGRect(
                    x: visibleFrameOfScreen.minX + visibleFrameOfScreen.width / 2,
                    y: visibleFrameOfScreen.minY + visibleFrameOfScreen.height / 2,
                    width: visibleFrameOfScreen.width / 2,
                    height: visibleFrameOfScreen.height / 2
                ))

        case .bottomLeftQuarter:
            // For bottom-aligned windows, use Rectangle's approach: don't manually adjust the Y-coordinate
            let width = floor(visibleFrameOfScreen.width / 2)
            let height = floor(visibleFrameOfScreen.height / 2)

            // Create a new rect with explicit coordinates to avoid inheriting from visibleFrameOfScreen
            let rect = CGRect(
                x: visibleFrameOfScreen.minX,  // Position at left edge of screen
                y: visibleFrameOfScreen.minY,  // Set to bottom of visible frame (accounts for dock)
                width: width,
                height: height
            )

            LoggingService.shared.debug(
                "Bottom left quarter calculation - width: \(width), height: \(height), x: \(rect.origin.x), y: \(rect.origin.y)",
                service: "StandardPositionCalculation",
                category: .windowCalculation
            )
            LoggingService.shared.debug(
                "Resulting rect: \(rect)",
                service: "StandardPositionCalculation",
                category: .windowCalculation
            )

            return RectResult(rect)

        case .bottomRightQuarter:
            // For bottom-aligned windows, use Rectangle's approach: don't manually adjust the Y-coordinate
            let width = floor(visibleFrameOfScreen.width / 2)
            let height = floor(visibleFrameOfScreen.height / 2)

            // Create a new rect with explicit coordinates to avoid inheriting from visibleFrameOfScreen
            let rect = CGRect(
                x: visibleFrameOfScreen.minX + width,  // Position at right half of screen
                y: visibleFrameOfScreen.minY,  // Set to bottom of visible frame (accounts for dock)
                width: width,
                height: height
            )

            LoggingService.shared.debug(
                "Bottom right quarter calculation - width: \(width), height: \(height), x: \(rect.origin.x), y: \(rect.origin.y)",
                service: "StandardPositionCalculation",
                category: .windowCalculation
            )
            LoggingService.shared.debug(
                "Resulting rect: \(rect)",
                service: "StandardPositionCalculation",
                category: .windowCalculation
            )

            return RectResult(rect)

        case .custom(let rect):
            return RectResult(rect)

        default:
            // Handle thirds and two-thirds cases in a separate method
            return calculateThirdsRect(params)
        }
    }

    private func calculateThirdsRect(_ params: RectCalculationParameters) -> RectResult {
        let visibleFrameOfScreen = params.visibleFrameOfScreen
        let thirdWidth = floor(visibleFrameOfScreen.width / 3)

        switch params.action {
        case .leftThird:
            return RectResult(
                CGRect(
                    x: visibleFrameOfScreen.minX,
                    y: visibleFrameOfScreen.minY,
                    width: thirdWidth,
                    height: visibleFrameOfScreen.height
                ))

        case .centerThird:
            return RectResult(
                CGRect(
                    x: visibleFrameOfScreen.minX + thirdWidth,
                    y: visibleFrameOfScreen.minY,
                    width: thirdWidth,
                    height: visibleFrameOfScreen.height
                ))

        case .rightThird:
            return RectResult(
                CGRect(
                    x: visibleFrameOfScreen.maxX - thirdWidth,
                    y: visibleFrameOfScreen.minY,
                    width: thirdWidth,
                    height: visibleFrameOfScreen.height
                ))

        case .leftTwoThirds:
            return RectResult(
                CGRect(
                    x: visibleFrameOfScreen.minX,
                    y: visibleFrameOfScreen.minY,
                    width: thirdWidth * 2,
                    height: visibleFrameOfScreen.height
                ))

        case .centerTwoThirds:
            return RectResult(
                CGRect(
                    x: visibleFrameOfScreen.minX + thirdWidth / 2,
                    y: visibleFrameOfScreen.minY,
                    width: thirdWidth * 2,
                    height: visibleFrameOfScreen.height
                ))

        case .rightTwoThirds:
            return RectResult(
                CGRect(
                    x: visibleFrameOfScreen.minX + thirdWidth,
                    y: visibleFrameOfScreen.minY,
                    width: thirdWidth * 2,
                    height: visibleFrameOfScreen.height
                ))

        default:
            return RectResult(.null)
        }
    }
}

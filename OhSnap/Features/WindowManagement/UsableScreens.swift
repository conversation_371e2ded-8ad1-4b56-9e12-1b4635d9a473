import AppKit
import Foundation

/// Represents a collection of screens and their relationships for window management
struct UsableScreens {
    let currentScreen: NSScreen
    let adjacentScreens: AdjacentScreens?
    let frameOfCurrentScreen: CGRect
    let numScreens: Int
    let screensOrdered: [NSScreen]
    
    init(
        currentScreen: NSScreen, 
        adjacentScreens: AdjacentScreens? = nil, 
        numScreens: Int,
        screensOrdered: [NSScreen]? = nil
    ) {
        self.currentScreen = currentScreen
        self.adjacentScreens = adjacentScreens
        self.frameOfCurrentScreen = currentScreen.frame
        self.numScreens = numScreens
        self.screensOrdered = screensOrdered ?? [currentScreen]
    }
    
    /// Convenience property to access screens array
    var screens: [NSScreen] {
        return screensOrdered
    }
}

/// Represents adjacent screens in a multi-display setup
struct AdjacentScreens {
    let prev: NSScreen
    let next: NSScreen
    
    init(prev: NSScreen, next: NSScreen) {
        self.prev = prev
        self.next = next
    }
}

import AppKit
import CoreGraphics
import Foundation

// Rectangle's exact structs - using Rectangle naming to avoid conflicts
struct RectangleWindow {
    let id: CGWindowID
    let rect: CGRect
}

struct RectangleWindowCalculationParameters {
    let window: RectangleWindow
    let usableScreens: UsableScreens
    let action: WindowDirection
    let lastAction: RectangleAction?
    let ignoreTodo: Bool

    func asRectParams(visibleFrame: CGRect? = nil, differentAction: WindowDirection? = nil)
        -> RectCalculationParameters
    {
        // Rectangle's calculations expect visibleFrameOfScreen to be in Rectangle's coordinate system (Y=0 at top)
        let appKitVisibleFrame =
            visibleFrame ?? usableScreens.currentScreen.adjustedVisibleFrame(ignoreTodo, false)
        let rectangleVisibleFrame = appKitVisibleFrame.screenFlipped

        return RectCalculationParameters(
            window: WindowInfo(
                frame: window.rect, monitorID: nil, appBundleIdentifier: nil, isFullscreen: false),
            visibleFrameOfScreen: rectangleVisibleFrame,
            action: differentAction ?? action,
            frameOfScreen: usableScreens.currentScreen.frame.screenFlipped,
            lastAction: lastAction?.action
        )
    }
}

struct RectangleWindowCalculationResult {
    var rect: CGRect
    let screen: NSScreen
    let resultingAction: WindowDirection
    let resultingSubAction: WindowDirection?
    let resultingScreenFrame: CGRect?

    init(
        rect: CGRect,
        screen: NSScreen,
        resultingAction: WindowDirection,
        resultingSubAction: WindowDirection? = nil,
        resultingScreenFrame: CGRect? = nil
    ) {

        self.rect = rect
        self.screen = screen
        self.resultingAction = resultingAction
        self.resultingSubAction = resultingSubAction
        self.resultingScreenFrame = resultingScreenFrame
    }
}

struct RectangleAction {
    let action: WindowDirection
    let subAction: WindowDirection?
    let rect: CGRect
    let count: Int
}

class WindowCalculationService {
    private let screenDetection: ScreenDetectionService
    private let logger = LoggingService.shared
    private let serviceName = "WindowCalculationService"

    // Rectangle's exact calculation factory approach - matching WindowCalculationFactory
    private let calculationsByAction: [WindowDirection: WindowCalculation] = [
        .leftHalf: LeftHalfCalculation(),
        .rightHalf: RightHalfCalculation(),
        .topHalf: TopHalfCalculation(),
        .bottomHalf: BottomHalfCalculation(),
        .maximize: MaximizeCalculation(),
        .centerTwoThirds: CenterTwoThirdsCalculation(),
        .almostMaximize: AlmostMaximizeCalculation(),
        .larger: ChangeSizeCalculation(),
        .smaller: ChangeSizeCalculation(),
        .largerWidth: ChangeSizeCalculation(),
        .smallerWidth: ChangeSizeCalculation(),
        .specified: SpecifiedCalculation(),
    ]

    init(screenDetection: ScreenDetectionService = ScreenDetectionService()) {
        self.screenDetection = screenDetection
        logger.info(
            "Initialized with Rectangle's exact calculation factory approach", service: serviceName)
    }

    // Rectangle's exact execute flow - matching WindowManager.execute()
    func calculateWindowRect(
        for direction: WindowDirection,
        window: WindowInfo,
        screen: NSScreen,
        visibleFrameOnly: Bool = true,
        lastAction: WindowDirection? = nil
    ) -> CGRect {

        logger.debug(
            "Starting Rectangle's exact calculation pipeline for direction: \(direction)",
            service: serviceName)

        // Step 1: Create UsableScreens like Rectangle does
        let allScreens = screenDetection.getAllScreens()
        let usableScreens = UsableScreens(
            currentScreen: screen,
            adjacentScreens: nil,
            numScreens: allScreens.count,
            screensOrdered: allScreens
        )

        // Step 2: Convert current window rect to Rectangle's coordinate system (screenFlipped)
        let currentNormalizedRect = window.frame.screenFlipped
        let currentWindow = RectangleWindow(id: 0, rect: currentNormalizedRect)

        // Step 3: Create Rectangle's exact WindowCalculationParameters
        let lastRectangleAction = lastAction.map {
            RectangleAction(action: $0, subAction: nil, rect: currentNormalizedRect, count: 1)
        }
        let calculationParams = RectangleWindowCalculationParameters(
            window: currentWindow,
            usableScreens: usableScreens,
            action: direction,
            lastAction: lastRectangleAction,
            ignoreTodo: false
        )

        // Step 4: Get calculation from Rectangle's factory - matching WindowCalculationFactory.calculationsByAction[action]
        guard let windowCalculation = calculationsByAction[direction] else {
            logger.error("No calculation found for direction: \(direction)", service: serviceName)
            return window.frame
        }

        // Step 5: Execute Rectangle's exact calculation - matching windowCalculation?.calculate(calculationParams)
        guard let calcResult = windowCalculation.calculate(calculationParams) else {
            logger.error(
                "Calculation returned nil result for direction: \(direction)", service: serviceName)
            return window.frame
        }

        logger.debug(
            "Rectangle calculation result (before screenFlipped): \(calcResult.rect)",
            service: serviceName)

        // Step 6: Rectangle's final step - flip coordinates back for AX system (matching apply() method line 176)
        let finalRect = calcResult.rect.screenFlipped

        logger.debug("Final rect (after screenFlipped): \(finalRect)", service: serviceName)
        return finalRect
    }
}

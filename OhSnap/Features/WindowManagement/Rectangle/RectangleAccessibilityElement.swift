//
//  AccessibilityElement.swift
//  Rectangle, Ported from Spectacle
//
//  Created by <PERSON> on 6/12/19.
//  Copyright © 2019 <PERSON>. All rights reserved.
//

import Cocoa

class RectangleAccessibilityElement {
    fileprivate let wrappedElement: AXUIElement

    init(_ element: AXUIElement) {
        wrappedElement = element
    }

    convenience init(_ pid: pid_t) {
        self.init(AXUIElementCreateApplication(pid))
    }

    convenience init?(_ bundleIdentifier: String) {
        guard
            let app =
                (NSWorkspace.shared.runningApplications.first {
                    $0.bundleIdentifier == bundleIdentifier
                })
        else { return nil }
        self.init(app.processIdentifier)
    }

    convenience init?(_ position: CGPoint) {
        guard let element = AXUIElement.systemWide.getElementAtPosition(position) else {
            return nil
        }
        self.init(element)
    }

    var role: NSAccessibility.Role? {
        wrappedElement.getValue(.role) as? NSAccessibility.Role
    }

    var subrole: NSAccessibility.Subrole? {
        wrappedElement.getValue(.subrole) as? NSAccessibility.Subrole
    }

    var isWindow: Bool? {
        role == .window
    }

    var isApplication: Bool? {
        role == .application
    }

    var isSystemDialog: Bool? {
        subrole == .systemDialog
    }

    var position: CGPoint? {
        get {
            wrappedElement.getWrappedValue(.position)
        }
        set {
            guard let newValue = newValue else { return }
            wrappedElement.setValue(.position, newValue)
        }
    }

    func isResizable() -> Bool {
        if let isResizable = wrappedElement.isValueSettable(.size) {
            return isResizable
        }
        LoggingService.shared.warning(
            "Unable to determine if window is resizeable. Assuming it is.",
            service: "AccessibilityElement")
        return true
    }

    var size: CGSize? {
        get {
            wrappedElement.getWrappedValue(.size)
        }
        set {
            guard let newValue = newValue else { return }
            wrappedElement.setValue(.size, newValue)
            LoggingService.shared.debug(
                "AX sizing proposed: \(newValue.debugDescription), result: \(size?.debugDescription ?? "N/A")",
                service: "AccessibilityElement")
        }
    }

    var frame: CGRect {
        guard let position = position, let size = size else { return .null }
        return .init(origin: position, size: size)
    }

    func setFrame(_ frame: CGRect, adjustSizeFirst: Bool = false) {
        var enhancedUI: Bool?
        let appElement = applicationElement

        if let appElement = appElement {
            enhancedUI = appElement.enhancedUserInterface
            if enhancedUI == true {
                LoggingService.shared.debug(
                    "AXEnhancedUserInterface was enabled, will disable before resizing",
                    service: "AccessibilityElement")
                appElement.enhancedUserInterface = false
            }
        }

        if adjustSizeFirst {
            size = frame.size
        }
        position = frame.origin
        size = frame.size

        // If "enhanced user interface" was originally enabled for the app, turn it back on
        if let appElement = appElement, enhancedUI == true {
            appElement.enhancedUserInterface = true
        }
    }

    private var childElements: [AccessibilityElement]? {
        getElementsValue(.children)
    }

    func getChildElement(_ role: NSAccessibility.Role) -> AccessibilityElement? {
        return childElements?.first { $0.role == role }
    }

    func getElementValue(_ attribute: NSAccessibility.Attribute) -> AccessibilityElement? {
        guard let element = wrappedElement.getValue(attribute) else { return nil }
        return AccessibilityElement(element as! AXUIElement)
    }

    func getElementsValue(_ attribute: NSAccessibility.Attribute) -> [AccessibilityElement]? {
        guard let elements = wrappedElement.getValue(attribute) as? [AXUIElement] else {
            return nil
        }
        return elements.map { AccessibilityElement($0) }
    }

    func getSelfOrChildElementRecursively(_ position: CGPoint) -> AccessibilityElement? {
        func getChildElement() -> AccessibilityElement? {
            return element.childElements?
                .map { (element: $0, frame: $0.frame) }
                .filter { $0.frame.contains(position) }
                .min { $0.frame.width * $0.frame.height < $1.frame.width * $1.frame.height }?
                .element
        }
        var element = self
        var elements = Set<AccessibilityElement>()
        while let childElement = getChildElement(), elements.insert(childElement).inserted {
            element = childElement
        }
        return element
    }

    var windowId: CGWindowID? {
        wrappedElement.getWindowId()
    }

    func getWindowId() -> CGWindowID? {
        if let windowId = windowId {
            return windowId
        }
        let frame = frame
        // Take the first match because there's no real way to guarantee which window we're actually getting
        if let pid = pid,
            let info = (WindowUtil.getWindowList().first { $0.pid == pid && $0.frame == frame })
        {
            return info.id
        }
        LoggingService.shared.warning("Unable to obtain window id", service: "AccessibilityElement")
        return nil
    }

    var pid: pid_t? {
        wrappedElement.getPid()
    }

    var windowElement: AccessibilityElement? {
        if isWindow == true { return self }
        return getElementValue(.window)
    }

    private var isMainWindow: Bool? {
        get {
            windowElement?.wrappedElement.getValue(.main) as? Bool
        }
        set {
            guard let newValue = newValue else { return }
            windowElement?.wrappedElement.setValue(.main, newValue)
        }
    }

    var isMinimized: Bool? {
        windowElement?.wrappedElement.getValue(.minimized) as? Bool
    }

    var isFullScreen: Bool? {
        guard let subrole = windowElement?.getElementValue(.fullScreenButton)?.subrole else {
            return nil
        }
        return subrole == .zoomButton
    }

    var titleBarFrame: CGRect? {
        guard
            let windowElement,
            case let windowFrame = windowElement.frame,
            windowFrame != .null,
            let closeButtonFrame = windowElement.getChildElement(.closeButton)?.frame,
            closeButtonFrame != .null
        else {
            return nil
        }
        let gap = closeButtonFrame.minY - windowFrame.minY
        let height = 2 * gap + closeButtonFrame.height
        return CGRect(
            origin: windowFrame.origin, size: CGSize(width: windowFrame.width, height: height))
    }

    func bringToFront(force: Bool = false) {
        if force {
            isMainWindow = true
        }

        if let pid = pid {
            let app = NSRunningApplication(processIdentifier: pid)
            app?.activate(options: .activateIgnoringOtherApps)
        }
    }

    private var applicationElement: AccessibilityElement? {
        if isApplication == true { return self }
        guard let pid = pid else { return nil }
        return AccessibilityElement(pid)
    }

    private var focusedWindowElement: AccessibilityElement? {
        applicationElement?.getElementValue(.focusedWindow)
    }

    var windowElements: [AccessibilityElement]? {
        applicationElement?.getElementsValue(.windows)
    }

    var isHidden: Bool? {
        applicationElement?.wrappedElement.getValue(.hidden) as? Bool
    }

    var enhancedUserInterface: Bool? {
        get {
            applicationElement?.wrappedElement.getValue(.enhancedUserInterface) as? Bool
        }
        set {
            guard let newValue = newValue else { return }
            applicationElement?.wrappedElement.setValue(.enhancedUserInterface, newValue)
        }
    }
    static func getFrontApplicationElement() -> AccessibilityElement? {
        guard let app = NSWorkspace.shared.frontmostApplication else { return nil }
        return AccessibilityElement(app.processIdentifier)
    }

    static func getFrontWindowElement() -> AccessibilityElement? {
        guard let appElement = getFrontApplicationElement() else {
            LoggingService.shared.warning(
                "Failed to find the application that currently has focus.",
                service: "AccessibilityElement")
            return nil
        }
        if let focusedWindowElement = appElement.focusedWindowElement {
            return focusedWindowElement
        }
        if let firstWindowElement = appElement.windowElements?.first {
            return firstWindowElement
        }
        LoggingService.shared.warning(
            "Failed to find frontmost window.", service: "AccessibilityElement")
        return nil
    }

    static func getAllWindowElements() -> [AccessibilityElement] {
        var windowElements: [AccessibilityElement] = []

        for runningApp in NSWorkspace.shared.runningApplications {
            if runningApp.activationPolicy != .regular { continue }

            let appElement = AccessibilityElement(runningApp.processIdentifier)
            if let windows = appElement.windowElements {
                windowElements.append(contentsOf: windows)
            }
        }

        return windowElements
    }

    static func getWindowElement(at position: CGPoint, systemWideFirst: Bool = true)
        -> AccessibilityElement?
    {
        func getWindowInfo(_ position: CGPoint) -> WindowInfo? {
            // This would need WindowUtil implementation
            return nil
        }

        if let info = getWindowInfo(position) {
            if let windowElements = AccessibilityElement(info.pid).windowElements {
                if let windowElement = (windowElements.first { $0.windowId == info.id }) {
                    return windowElement
                }
                if let windowElement = (windowElements.first { $0.frame == info.frame }) {
                    return windowElement
                }
            }
        }

        if !systemWideFirst,
            let element = AccessibilityElement(position),
            let windowElement = element.windowElement
        {

            if let pid = windowElement.pid {
                let appName = NSRunningApplication(processIdentifier: pid)?.localizedName ?? ""
                LoggingService.shared.debug(
                    "Window under cursor fallback matched: \(appName)",
                    service: "AccessibilityElement")
            }
            return windowElement
        }
        LoggingService.shared.warning(
            "Unable to obtain the accessibility element with the specified attribute at mouse location",
            service: "AccessibilityElement")
        return nil
    }

    static func getWindowElement(_ windowId: CGWindowID) -> AccessibilityElement? {
        // This would need WindowUtil implementation
        return nil
    }
}

// MARK: - Hashable
extension AccessibilityElement: Hashable {
    static func == (lhs: AccessibilityElement, rhs: AccessibilityElement) -> Bool {
        return CFEqual(lhs.wrappedElement, rhs.wrappedElement)
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(CFHash(wrappedElement))
    }
}

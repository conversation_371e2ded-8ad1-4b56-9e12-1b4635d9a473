import AppKit
import CoreGraphics

extension CGRect {
    /// Returns the center point of the rectangle
    var center: CGPoint {
        return CGPoint(x: midX, y: midY)
    }

    /// Flips the Y coordinate for the AX coordinate system
    /// Fixed to use the main screen as reference instead of screens[0]
    /// This prevents coordinate conversion issues in vertical arrangements
    var screenFlipped: CGRect {
        guard !isNull else {
            return self
        }

        // Use main screen as reference for coordinate flipping instead of screens[0]
        // In vertical arrangements, screens[0] might not be the main screen,
        // causing incorrect coordinate conversion for bottom half calculations
        let referenceScreen = NSScreen.main ?? NSScreen.screens.first!

        LoggingService.shared.debug(
            "Coordinate flip - using reference screen: \(referenceScreen.frame), main: \(NSScreen.main?.frame ?? CGRect.zero)",
            service: "CGRectExtensions"
        )

        return .init(
            origin: .init(x: origin.x, y: referenceScreen.frame.maxY - maxY), size: size)
    }

    /// Returns true if the rectangle is wider than it is tall
    var isLandscape: Bool { width > height }

    /// Returns the number of edges shared with another rectangle
    func numSharedEdges(withRect rect: CGRect) -> Int {
        var sharedEdgeCount = 0
        if minX == rect.minX { sharedEdgeCount += 1 }
        if maxX == rect.maxX { sharedEdgeCount += 1 }
        if minY == rect.minY { sharedEdgeCount += 1 }
        if maxY == rect.maxY { sharedEdgeCount += 1 }
        return sharedEdgeCount
    }
}

extension CGPoint {
    /// Flips the Y coordinate for the AX coordinate system
    /// Fixed to use the main screen as reference instead of screens[0]
    var screenFlipped: CGPoint {
        let referenceScreen = NSScreen.main ?? NSScreen.screens.first!
        return .init(x: x, y: referenceScreen.frame.maxY - y)
    }
}

import AppKit
import CoreGraphics
import Foundation

// Rectangle's EXACT structs - copied exactly from Rectangle
struct Window {
    let id: CGWindowID
    let rect: CGRect
}

struct WindowCalculationParameters {
    let window: Window
    let usableScreens: UsableScreens
    let action: WindowDirection
    let lastAction: RectangleAction?
    let ignoreTodo: Bool

    func asRectParams(visibleFrame: CGRect? = nil, differentAction: WindowDirection? = nil) -> RectCalculationParameters {
        RectCalculationParameters(
            window: WindowInfo(frame: window.rect, monitorID: nil, appBundleIdentifier: nil, isFullscreen: false),
            visibleFrameOfScreen: visibleFrame ?? usableScreens.currentScreen.adjustedVisibleFrame(ignoreTodo, false),
            action: differentAction ?? action,
            frameOfScreen: usableScreens.currentScreen.frame,
            lastAction: lastAction?.action
        )
    }
}

struct WindowCalculationResult {
    var rect: CGRect
    let screen: NSScreen
    let resultingAction: WindowDirection
    let resultingSubAction: WindowDirection?
    let resultingScreenFrame: CGRect?

    init(rect: CGRect,
         screen: NSScreen,
         resultingAction: WindowDirection,
         resultingSubAction: WindowDirection? = nil,
         resultingScreenFrame: CGRect? = nil) {

        self.rect = rect
        self.screen = screen
        self.resultingAction = resultingAction
        self.resultingSubAction = resultingSubAction
        self.resultingScreenFrame = resultingScreenFrame
    }
}

struct RectangleAction {
    let action: WindowDirection
    let subAction: WindowDirection?
    let rect: CGRect
    let count: Int
}

class WindowCalculationService {
    private let screenDetection: ScreenDetectionService
    private let logger = LoggingService.shared
    private let serviceName = "WindowCalculationService"

    // Rectangle's EXACT calculationsByAction - copied exactly from WindowCalculationFactory
    private let calculationsByAction: [WindowDirection: WindowCalculation] = [
        .leftHalf: LeftHalfCalculation(),
        .rightHalf: RightHalfCalculation(),
        .topHalf: TopHalfCalculation(),
        .bottomHalf: BottomHalfCalculation(),
        .maximize: MaximizeCalculation(),
        .centerTwoThirds: CenterTwoThirdsCalculation(),
        .almostMaximize: AlmostMaximizeCalculation(),
        .larger: ChangeSizeCalculation(),
        .smaller: ChangeSizeCalculation(),
        .largerWidth: ChangeSizeCalculation(),
        .smallerWidth: ChangeSizeCalculation(),
        .specified: SpecifiedCalculation()
    ]

    init(screenDetection: ScreenDetectionService = ScreenDetectionService()) {
        self.screenDetection = screenDetection
        logger.info("Initialized with Rectangle's EXACT calculation factory", service: serviceName)
    }

    // Rectangle's EXACT execute flow - copied exactly from WindowManager.execute()
    func calculateWindowRect(
        for direction: WindowDirection,
        window: WindowInfo,
        screen: NSScreen,
        visibleFrameOnly: Bool = true,
        lastAction: WindowDirection? = nil
    ) -> CGRect {

        logger.debug("Rectangle's EXACT calculation pipeline for direction: \(direction)", service: serviceName)

        // Rectangle's EXACT flow - line 105-106
        let currentNormalizedRect = window.frame.screenFlipped
        let currentWindow = Window(id: 0, rect: currentNormalizedRect)

        // Rectangle's EXACT flow - line 67
        let usableScreens = UsableScreens(
            currentScreen: screen,
            adjacentScreens: nil,
            numScreens: screenDetection.getAllScreens().count,
            screensOrdered: screenDetection.getAllScreens()
        )

        // Rectangle's EXACT flow - line 108
        let windowCalculation = calculationsByAction[direction]

        // Rectangle's EXACT flow - line 110
        let lastRectangleAction = lastAction.map { RectangleAction(action: $0, subAction: nil, rect: currentNormalizedRect, count: 1) }
        let calculationParams = WindowCalculationParameters(
            window: currentWindow,
            usableScreens: usableScreens,
            action: direction,
            lastAction: lastRectangleAction,
            ignoreTodo: false
        )

        // Rectangle's EXACT flow - line 111
        guard let calcResult = windowCalculation?.calculate(calculationParams) else {
            logger.error("Nil calculation result", service: serviceName)
            return window.frame
        }

        logger.debug("Rectangle calculation result: \(calcResult.rect)", service: serviceName)

        // Rectangle's EXACT flow - line 176: apply() method
        let newRect = calcResult.rect.screenFlipped

        logger.debug("Final rect after screenFlipped: \(newRect)", service: serviceName)
        return newRect
    }
}
//
//  AXUIElementExtensions.swift
//  Rectangle
//
//  Created by <PERSON> on 6/12/19.
//  Copyright © 2019 <PERSON>. All rights reserved.
//

import Cocoa

extension AXUIElement {
    
    static var systemWide: AXUIElement {
        return AXUIElementCreateSystemWide()
    }
    
    func getValue(_ attribute: NSAccessibility.Attribute) -> AnyObject? {
        var value: AnyObject?
        let result = AXUIElementCopyAttributeValue(self, attribute.rawValue as CFString, &value)
        return result == .success ? value : nil
    }
    
    func setValue(_ attribute: NSAccessibility.Attribute, _ value: AnyObject) {
        AXUIElementSetAttributeValue(self, attribute.rawValue as CFString, value)
    }
    
    func isValueSettable(_ attribute: NSAccessibility.Attribute) -> Bool? {
        var settable: DarwinBoolean = false
        let result = AXUIElementIsAttributeSettable(self, attribute.rawValue as CFString, &settable)
        return result == .success ? settable.boolValue : nil
    }
    
    func getWrappedValue<T>(_ attribute: NSAccessibility.Attribute) -> T? {
        guard let value = getValue(attribute) else { return nil }
        
        if let axValue = value as? AXValue {
            switch attribute {
            case .position:
                var point = CGPoint.zero
                AXValueGetValue(axValue, .cgPoint, &point)
                return point as? T
            case .size:
                var size = CGSize.zero
                AXValueGetValue(axValue, .cgSize, &size)
                return size as? T
            default:
                return nil
            }
        }
        
        return value as? T
    }
    
    func getElementAtPosition(_ position: CGPoint) -> AXUIElement? {
        var element: AXUIElement?
        let result = AXUIElementCopyElementAtPosition(self, Float(position.x), Float(position.y), &element)
        return result == .success ? element : nil
    }
    
    func getWindowId() -> CGWindowID? {
        // Try to get window ID directly from accessibility element
        if let windowId = getValue(.windowId) as? CGWindowID {
            return windowId
        }
        
        // Fallback: try to get from window info
        guard let pid = getPid() else { return nil }
        
        let windowList = CGWindowListCopyWindowInfo([.optionOnScreenOnly, .excludeDesktopElements], kCGNullWindowID) as? [[String: Any]]
        
        for windowInfo in windowList ?? [] {
            if let windowPid = windowInfo[kCGWindowOwnerPID as String] as? pid_t,
               windowPid == pid,
               let windowId = windowInfo[kCGWindowNumber as String] as? CGWindowID {
                return windowId
            }
        }
        
        return nil
    }
    
    func getPid() -> pid_t? {
        var pid: pid_t = 0
        let result = AXUIElementGetPid(self, &pid)
        return result == .success ? pid : nil
    }
}

// MARK: - WindowInfo struct for compatibility
struct WindowInfo {
    let id: CGWindowID
    let pid: pid_t
    let frame: CGRect
}

// MARK: - WindowUtil placeholder
class WindowUtil {
    static func getWindowList(ids: [CGWindowID]? = nil) -> [WindowInfo] {
        let windowList = CGWindowListCopyWindowInfo([.optionOnScreenOnly, .excludeDesktopElements], kCGNullWindowID) as? [[String: Any]]
        
        var windows: [WindowInfo] = []
        
        for windowInfo in windowList ?? [] {
            guard let windowId = windowInfo[kCGWindowNumber as String] as? CGWindowID,
                  let pid = windowInfo[kCGWindowOwnerPID as String] as? pid_t,
                  let bounds = windowInfo[kCGWindowBounds as String] as? [String: Any],
                  let x = bounds["X"] as? CGFloat,
                  let y = bounds["Y"] as? CGFloat,
                  let width = bounds["Width"] as? CGFloat,
                  let height = bounds["Height"] as? CGFloat else {
                continue
            }
            
            if let ids = ids, !ids.contains(windowId) {
                continue
            }
            
            let frame = CGRect(x: x, y: y, width: width, height: height)
            windows.append(WindowInfo(id: windowId, pid: pid, frame: frame))
        }
        
        return windows
    }
}

//
//  BottomHalfCalculation.swift
//  Rectangle, Ported from Spectacle
//
//  Created by <PERSON> on 6/14/19.
//  Copyright © 2019 <PERSON>. All rights reserved.
//

import Foundation

class BottomHalfCalculation: WindowCalculation, RepeatedExecutionsInThirdsCalculation {

    override func calculateRect(_ params: RectCalculationParameters) -> RectResult {

        if params.lastAction == nil || !DefaultsManager.shared.subsequentExecutionMode.resizes {
            return calculateFirstRect(params)
        }

        return calculateRepeatedRect(params)
    }

    func calculateFractionalRect(_ params: RectCalculationParameters, fraction: Float) -> RectResult
    {
        let visibleFrameOfScreen = params.visibleFrameOfScreen

        var rect = visibleFrameOfScreen
        rect.size.height = floor(visibleFrameOfScreen.height * CGFloat(fraction))
        // Position at bottom of visible frame (above dock) - in Rectangle's flipped coordinates
        rect.origin.y = visibleFrameOfScreen.maxY - rect.height
        return RectResult(rect)
    }

}

import AppKit
import Foundation

// Base protocol for all calculations
protocol Calculation {
    func calculateRect(_ params: RectCalculationParameters) -> RectResult
}

// Parameters for rectangle calculations
struct RectCalculationParameters {
    let window: WindowInfo
    let visibleFrameOfScreen: CGRect
    let action: WindowDirection
    let frameOfScreen: CGRect
    let lastAction: WindowDirection?

    init(
        window: WindowInfo, visibleFrameOfScreen: CGRect, action: WindowDirection,
        frameOfScreen: CGRect, lastAction: WindowDirection? = nil
    ) {
        self.window = window
        self.visibleFrameOfScreen = visibleFrameOfScreen
        self.action = action
        self.frameOfScreen = frameOfScreen
        self.lastAction = lastAction
    }
}

// Result of a rectangle calculation
struct RectResult {
    let rect: CGRect
    let subAction: WindowDirection?

    init(_ rect: CGRect, subAction: WindowDirection? = nil) {
        self.rect = rect
        self.subAction = subAction
    }
}

// Old structs removed - now using Rectangle's EXACT structs from WindowCalculationService

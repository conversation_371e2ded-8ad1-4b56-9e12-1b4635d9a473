import AppKit
import Carbon

// Rectangle-compatible AccessibilityElement wrapper for screen detection
class RectangleAccessibilityElement {
    private let element: AXUIElement

    init(_ element: AXUIElement) {
        self.element = element
    }

    var frame: CGRect {
        // Get frame synchronously using direct AX calls
        var positionRef: CFTypeRef?
        var sizeRef: CFTypeRef?

        let positionError = AXUIElementCopyAttributeValue(
            element, kAXPositionAttribute as CFString, &positionRef)
        let sizeError = AXUIElementCopyAttributeValue(
            element, kAXSizeAttribute as CFString, &sizeRef)

        guard positionError == .success, sizeError == .success,
            let positionValue = positionRef, let sizeValue = sizeRef
        else {
            return CGRect.zero
        }

        var position = CGPoint.zero
        var size = CGSize.zero
        AXValueGetValue(positionValue as! AXValue, .cgPoint, &position)
        AXValueGetValue(sizeValue as! AXValue, .cgSize, &size)

        return CGRect(origin: position, size: size)
    }
}

// Adapted from Rectangle (https://github.com/rxhanson/Rectangle) under GPL-3.0
class WindowSnappingService {
    // MARK: - Constants
    private let serviceName = "WindowSnappingService"
    private let logger = LoggingService.shared
    private let screenDetection = ScreenDetectionService()

    // MARK: - Public Methods
    func snapFrontmostWindow(to position: SnapPosition) {
        logger.info("┌─── Rectangle Window Snapping ───┐", service: serviceName)
        logger.info("│ Snapping to position: \(position)", service: serviceName)

        // Get frontmost window using Rectangle's approach
        guard let frontmostWindowElement = AccessibilityElement.getFrontWindowElement() else {
            logger.error("│ Failed to get frontmost window", service: serviceName)
            logger.info("└─────────────────────────────────┘", service: serviceName)
            return
        }

        // Use Rectangle's exact screen detection
        guard let usableScreens = screenDetection.detectScreens(using: frontmostWindowElement)
        else {
            logger.error("│ Failed to detect screens", service: serviceName)
            logger.info("└─────────────────────────────────┘", service: serviceName)
            return
        }

        logger.debug("│ Detected \(usableScreens.numScreens) screens", service: serviceName)
        logger.debug("│ Current screen: \(usableScreens.currentScreen.frame)", service: serviceName)

        // Get current window frame and convert using Rectangle's coordinate system
        let currentWindowRect = frontmostWindowElement.frame
        let currentNormalizedRect = currentWindowRect.screenFlipped

        logger.debug("│ Window frame (AX): \(currentWindowRect)", service: serviceName)
        logger.debug("│ Window frame (normalized): \(currentNormalizedRect)", service: serviceName)

        // Use Rectangle's exact calculation pipeline through WindowCalculationService
        let windowInfo = WindowInfo(
            frame: currentWindowRect,  // Use original AX coordinates
            monitorID: nil,
            appBundleIdentifier: nil,
            isFullscreen: false
        )

        let windowDirection = convertSnapPositionToWindowDirection(position)
        let calculationService = WindowCalculationService()

        let newRect = calculationService.calculateWindowRect(
            for: windowDirection,
            window: windowInfo,
            screen: usableScreens.currentScreen
        )
        logger.debug("│ New rect (AX): \(newRect)", service: serviceName)

        frontmostWindowElement.setFrame(newRect)

        logger.info(
            "│ Successfully snapped window using Rectangle's approach", service: serviceName)
        logger.info("└─────────────────────────────────┘", service: serviceName)
    }

    // MARK: - Helper Methods

    private func convertSnapPositionToWindowDirection(_ position: SnapPosition) -> WindowDirection {
        switch position {
        case .leftHalf: return .leftHalf
        case .rightHalf: return .rightHalf
        case .topHalf: return .topHalf
        case .bottomHalf: return .bottomHalf
        case .fullscreen: return .maximize
        case .leftThird: return .leftThird
        case .centerThird: return .centerThird
        case .rightThird: return .rightThird
        case .topLeftQuarter: return .topLeftQuarter
        case .topRightQuarter: return .topRightQuarter
        case .bottomLeftQuarter: return .bottomLeftQuarter
        case .bottomRightQuarter: return .bottomRightQuarter
        case .leftTwoThirds: return .leftTwoThirds
        case .centerTwoThirds: return .centerTwoThirds
        case .rightTwoThirds: return .rightTwoThirds
        default: return .maximize
        }
    }
}

// MARK: - Error Types
enum WindowSnappingError: Error, CustomStringConvertible {
    case noFrontmostWindow
    case noTargetScreen

    var description: String {
        switch self {
        case .noFrontmostWindow:
            return "No frontmost window found"
        case .noTargetScreen:
            return "No target screen found"
        }
    }
}

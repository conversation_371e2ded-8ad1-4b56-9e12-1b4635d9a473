import AppKit

// Adapted from Rectangle (https://github.com/rxhanson/Rectangle) under GPL-3.0
// This matches <PERSON>ctangle's exact NSScreen extension

extension NSScreen {

    func adjustedVisibleFrame(_ ignoreTodo: Bool = false, _ ignoreStage: Bool = false) -> CGRect {
        var newFrame = visibleFrame

        if !ignoreStage && DefaultsManager.shared.stageEnabled {
            if StageUtil.stageCapable && StageUtil.stageEnabled && StageUtil.stageStripShow {
                let stageSize = CGFloat(200)  // Default stage size, could be configurable

                if StageUtil.stageStripPosition == .left {
                    newFrame.origin.x += stageSize
                }
                newFrame.size.width -= stageSize
            }
        }

        // Apply minimal screen edge gaps (simplified version)
        let gapSize = CGFloat(0)  // Default to no gaps for now

        newFrame.origin.x += gapSize
        newFrame.origin.y += gapSize
        newFrame.size.width -= (gapSize * 2)
        newFrame.size.height -= (gapSize * 2)

        return newFrame
    }
}

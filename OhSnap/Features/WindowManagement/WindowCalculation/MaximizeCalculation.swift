//
//  MaximizeCalculation.swift
//  Rectangle - Exact implementation from Rectangle
//
//  Created by <PERSON> on 6/13/19.
//  Copyright © 2019 <PERSON>. All rights reserved.
//

import Cocoa

class MaximizeCalculation: WindowCalculation {

    override func calculateRect(_ params: RectCalculationParameters) -> RectResult {
        
        let visibleFrameOfScreen = params.visibleFrameOfScreen
        
        return RectResult(visibleFrameOfScreen)
    }
    
}
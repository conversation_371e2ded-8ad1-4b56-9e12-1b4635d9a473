//
//  CenterTwoThirdsCalculation.swift
//  Rectangle - Exact implementation from Rectangle
//
//  Created by <PERSON> on 6/13/19.
//  Copyright © 2019 <PERSON>. All rights reserved.
//

import Cocoa

class CenterTwoThirdsCalculation: WindowCalculation {

    override func calculateRect(_ params: RectCalculationParameters) -> RectResult {
        
        let visibleFrameOfScreen = params.visibleFrameOfScreen
        
        var rect = visibleFrameOfScreen
        rect.size.width = floor(visibleFrameOfScreen.width * 2.0 / 3.0)
        rect.origin.x = visibleFrameOfScreen.minX + floor((visibleFrameOfScreen.width - rect.width) / 2.0)
        
        return RectResult(rect)
    }
    
}
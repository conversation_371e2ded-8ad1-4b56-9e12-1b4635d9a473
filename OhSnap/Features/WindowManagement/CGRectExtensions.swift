import AppKit
import CoreGraphics

extension CGRect {
    /// Returns the center point of the rectangle
    var center: CGPoint {
        return CGPoint(x: midX, y: midY)
    }

    /// Flips the Y coordinate for the AX coordinate system
    /// This matches Rectangle's implementation exactly
    var screenFlipped: CGRect {
        guard !isNull else {
            return self
        }
        return .init(
            origin: .init(x: origin.x, y: NSScreen.screens[0].frame.maxY - maxY), size: size)
    }

    /// Flips the Y coordinate for the AX coordinate system using a specific screen
    /// This is more accurate for multi-display setups
    func screenFlipped(for screen: NSScreen) -> CGRect {
        guard !isNull else {
            return self
        }
        return .init(
            origin: .init(x: origin.x, y: screen.frame.maxY - maxY), size: size)
    }

    /// Returns true if the rectangle is wider than it is tall
    var isLandscape: Bool { width > height }

    var centerPoint: CGPoint {
        NSMakePoint(NSMidX(self), NSMidY(self))
    }

    func numSharedEdges(withRect rect: CGRect) -> Int {
        var sharedEdgeCount = 0
        if minX == rect.minX { sharedEdgeCount += 1 }
        if maxX == rect.maxX { sharedEdgeCount += 1 }
        if minY == rect.minY { sharedEdgeCount += 1 }
        if maxY == rect.maxY { sharedEdgeCount += 1 }
        return sharedEdgeCount
    }

    /// Checks if two rectangles are equal within a small tolerance
    func equalTo(_ other: CGRect) -> Bool {
        let tolerance: CGFloat = 1.0
        return abs(origin.x - other.origin.x) < tolerance
            && abs(origin.y - other.origin.y) < tolerance
            && abs(size.width - other.size.width) < tolerance
            && abs(size.height - other.size.height) < tolerance
    }
}

extension CGPoint {
    var screenFlipped: CGPoint {
        .init(x: x, y: NSScreen.screens[0].frame.maxY - y)
    }
}

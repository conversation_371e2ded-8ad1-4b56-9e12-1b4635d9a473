import AppKit
import Foundation

// Base protocol for all calculations
protocol Calculation {
    func calculateRect(_ params: RectCalculationParameters) -> RectResult
}

// Parameters for rectangle calculations
struct RectCalculationParameters {
    let window: WindowInfo
    let visibleFrameOfScreen: CGRect
    let action: WindowDirection
    let frameOfScreen: CGRect

    init(
        window: WindowInfo, visibleFrameOfScreen: CGRect, action: WindowDirection,
        frameOfScreen: CGRect
    ) {
        self.window = window
        self.visibleFrameOfScreen = visibleFrameOfScreen
        self.action = action
        self.frameOfScreen = frameOfScreen
    }
}

// Result of a rectangle calculation
struct RectResult {
    let rect: CGRect
    let subAction: WindowDirection?

    init(_ rect: CGRect, subAction: WindowDirection? = nil) {
        self.rect = rect
        self.subAction = subAction
    }
}

// Parameters for window calculations
struct WindowCalculationParameters {
    let window: WindowInfo
    let usableScreens: UsableScreens
    let action: WindowDirection

    init(window: WindowInfo, usableScreens: UsableScreens, action: WindowDirection) {
        self.window = window
        self.usableScreens = usableScreens
        self.action = action
    }

    func asRectParams() -> RectCalculationParameters {
        return RectCalculationParameters(
            window: window,
            visibleFrameOfScreen: usableScreens.currentScreen.visibleFrame,
            action: action,
            frameOfScreen: usableScreens.currentScreen.frame
        )
    }
}

// Result of a window calculation
struct WindowCalculationResult {
    let rect: CGRect
    let screen: NSScreen
    let resultingAction: WindowDirection
    let resultingSubAction: WindowDirection?

    init(
        rect: CGRect, screen: NSScreen, resultingAction: WindowDirection,
        resultingSubAction: WindowDirection? = nil
    ) {
        self.rect = rect
        self.screen = screen
        self.resultingAction = resultingAction
        self.resultingSubAction = resultingSubAction
    }
}

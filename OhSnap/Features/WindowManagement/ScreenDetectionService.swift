import AppKit

// Adapted from Rectangle (https://github.com/rxhanson/Rectangle) under GPL-3.0
// This is Rectangle's exact ScreenDetection implementation
class ScreenDetectionService {
    private let logger = LoggingService.shared
    private let serviceName = "ScreenDetectionService"

    func detectScreens(using frontmostWindowElement: RectangleAccessibilityElement?)
        -> UsableScreens?
    {
        let screens = NSScreen.screens
        guard let firstScreen = screens.first else { return nil }

        logger.debug("Detecting screens with \(screens.count) available", service: serviceName)

        if screens.count == 1 {
            let adjacentScreens = AdjacentScreens(prev: firstScreen, next: firstScreen)

            return UsableScreens(
                currentScreen: firstScreen,
                adjacentScreens: adjacentScreens,
                numScreens: screens.count,
                screensOrdered: [firstScreen]
            )
        }

        let screensOrdered = order(screens: screens)
        guard
            let sourceScreen: NSScreen = screenContaining(
                frontmostWindowElement?.frame ?? CGRect.zero,
                screens: screensOrdered
            )
        else {
            let adjacentScreens = AdjacentScreens(prev: firstScreen, next: firstScreen)
            return UsableScreens(
                currentScreen: firstScreen,
                adjacentScreens: adjacentScreens,
                numScreens: screens.count,
                screensOrdered: screensOrdered
            )
        }

        let adjacentScreens = adjacent(toFrameOfScreen: sourceScreen.frame, screens: screensOrdered)

        return UsableScreens(
            currentScreen: sourceScreen,
            adjacentScreens: adjacentScreens,
            numScreens: screens.count,
            screensOrdered: screensOrdered
        )
    }

    func screenContaining(_ rect: CGRect, screens: [NSScreen]) -> NSScreen? {
        var result: NSScreen? = NSScreen.main
        var largestPercentageOfRectWithinFrameOfScreen: CGFloat = 0.0

        logger.debug("Finding screen containing rect: \(rect)", service: serviceName)

        for currentScreen in screens {
            let currentFrameOfScreen = NSRectToCGRect(currentScreen.frame)
            let normalizedRect: CGRect = rect.screenFlipped

            if currentFrameOfScreen.contains(normalizedRect) {
                result = currentScreen
                logger.debug("Screen fully contains rect", service: serviceName)
                break
            }

            let percentageOfRectWithinCurrentFrameOfScreen: CGFloat = percentageOf(
                normalizedRect, withinFrameOfScreen: currentFrameOfScreen)

            if percentageOfRectWithinCurrentFrameOfScreen
                > largestPercentageOfRectWithinFrameOfScreen
            {
                largestPercentageOfRectWithinFrameOfScreen =
                    percentageOfRectWithinCurrentFrameOfScreen
                result = currentScreen
                logger.debug(
                    "New best screen by percentage: \(percentageOfRectWithinCurrentFrameOfScreen)",
                    service: serviceName)
            }
        }
        return result
    }

    func percentageOf(_ rect: CGRect, withinFrameOfScreen frameOfScreen: CGRect) -> CGFloat {
        let intersectionOfRectAndFrameOfScreen: CGRect = rect.intersection(frameOfScreen)
        var result: CGFloat = 0.0
        if !intersectionOfRectAndFrameOfScreen.isNull {
            result =
                computeAreaOfRect(rect: intersectionOfRectAndFrameOfScreen)
                / computeAreaOfRect(rect: rect)
        }
        return result
    }

    func adjacent(toFrameOfScreen frameOfScreen: CGRect, screens: [NSScreen]) -> AdjacentScreens? {
        if screens.count == 2 {
            let otherScreen = screens.first(where: { screen in
                let frame = NSRectToCGRect(screen.frame)
                return !frame.equalTo(frameOfScreen)
            })
            if let otherScreen = otherScreen {
                return AdjacentScreens(prev: otherScreen, next: otherScreen)
            }
        } else if screens.count > 2 {
            let currentScreenIndex = screens.firstIndex(where: { screen in
                let frame = NSRectToCGRect(screen.frame)
                return frame.equalTo(frameOfScreen)
            })
            if let currentScreenIndex = currentScreenIndex {
                let nextIndex = currentScreenIndex == screens.count - 1 ? 0 : currentScreenIndex + 1
                let prevIndex = currentScreenIndex == 0 ? screens.count - 1 : currentScreenIndex - 1
                return AdjacentScreens(prev: screens[prevIndex], next: screens[nextIndex])
            }
        }

        return nil
    }

    func order(screens: [NSScreen]) -> [NSScreen] {
        let sortedScreens = screens.sorted(by: { screen1, screen2 in
            // First, check if one screen is completely above the other
            if screen2.frame.maxY <= screen1.frame.minY {
                return true
            }
            if screen1.frame.maxY <= screen2.frame.minY {
                return false
            }

            // For screens that overlap vertically or share edges, sort by vertical center first
            let screen1CenterY = screen1.frame.midY
            let screen2CenterY = screen2.frame.midY

            // If vertical centers are significantly different, sort by center Y (top to bottom)
            if abs(screen1CenterY - screen2CenterY) > 50 {
                return screen1CenterY > screen2CenterY  // Higher Y values first (top to bottom in screen coordinates)
            }

            // If vertical centers are similar, sort by horizontal position (left to right)
            return screen1.frame.minX < screen2.frame.minX
        })

        logger.debug("Ordered \(sortedScreens.count) screens", service: serviceName)
        return sortedScreens
    }

    private func computeAreaOfRect(rect: CGRect) -> CGFloat {
        return rect.size.width * rect.size.height
    }

    // Legacy methods for compatibility
    func getScreenContaining(_ rect: CGRect) -> NSScreen? {
        return screenContaining(rect, screens: NSScreen.screens)
    }

    func getAllScreens() -> [NSScreen] {
        return NSScreen.screens
    }
}

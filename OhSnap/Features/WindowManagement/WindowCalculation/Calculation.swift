import AppKit
import Foundation

// Base protocol for all calculations
protocol Calculation {
    func calculateRect(_ params: RectCalculationParameters) -> RectResult
}

// Rectangle's EXACT RectCalculationParameters - copied exactly from Rectangle's WindowCalculation.swift
struct RectCalculationParameters {
    let window: Window
    let visibleFrameOfScreen: CGRect
    let action: WindowDirection
    let lastAction: RectangleAction?
}

// Result of a rectangle calculation
struct RectResult {
    let rect: CGRect
    let subAction: WindowDirection?

    init(_ rect: CGRect, subAction: WindowDirection? = nil) {
        self.rect = rect
        self.subAction = subAction
    }
}

// Old structs removed - now using Rectangle's EXACT structs from WindowCalculationService

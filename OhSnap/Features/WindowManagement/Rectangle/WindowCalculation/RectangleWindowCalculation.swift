//
//  WindowCalculation.swift
//  Rectangle, Ported from Spectacle
//
//  Created by <PERSON> on 6/13/19.
//  Copyright © 2019 <PERSON>. All rights reserved.
//

import Cocoa

protocol RectangleCalculation {

    func calculate(_ params: RectangleWindowCalculationParameters)
        -> RectangleWindowCalculationResult?

    func calculateRect(_ params: RectangleRectCalculationParameters) -> RectangleRectResult
}

class RectangleWindowCalculation: RectangleCalculation {

    func calculate(_ params: RectangleWindowCalculationParameters)
        -> RectangleWindowCalculationResult?
    {

        let rectResult = calculateRect(params.asRectParams())

        if rectResult.rect.isNull {
            return nil
        }

        return RectangleWindowCalculationResult(
            rect: rectResult.rect, screen: params.usableScreens.currentScreen,
            resultingAction: params.action, resultingSubAction: rectResult.subAction)
    }

    func calculateRect(_ params: RectangleRectCalculationParameters) -> RectangleRectResult {
        RectangleRectResult(.null)
    }

    func rectCenteredWithinRect(_ rect1: CGRect, _ rect2: CGRect) -> Bool {
        let centeredMidX = abs(rect2.midX - rect1.midX) <= 1.0
        let centeredMidY = abs(rect2.midY - rect1.midY) <= 1.0
        return rect1.contains(rect2) && centeredMidX && centeredMidY
    }
}

struct WindowCalculationParameters {
    let window: Window
    let usableScreens: UsableScreens
    let action: WindowAction
    let lastAction: RectangleAction?
    let ignoreTodo: Bool

    func asRectParams(visibleFrame: CGRect? = nil, differentAction: WindowAction? = nil)
        -> RectCalculationParameters
    {
        RectCalculationParameters(
            window: window,
            visibleFrameOfScreen: visibleFrame
                ?? usableScreens.currentScreen.adjustedVisibleFrame(ignoreTodo),
            action: differentAction ?? action,
            lastAction: lastAction)
    }

    func withDifferentAction(_ differentAction: WindowAction) -> WindowCalculationParameters {
        .init(
            window: window,
            usableScreens: usableScreens,
            action: differentAction,
            lastAction: lastAction,
            ignoreTodo: ignoreTodo)
    }
}

struct RectCalculationParameters {
    let window: Window
    let visibleFrameOfScreen: CGRect
    let action: WindowAction
    let lastAction: RectangleAction?
}

struct WindowCalculationResult {
    let rect: CGRect
    let screen: NSScreen
    let resultingAction: WindowAction
    let resultingSubAction: SubWindowAction?
    let resultingScreenFrame: CGRect?

    init(
        rect: CGRect, screen: NSScreen, resultingAction: WindowAction,
        resultingSubAction: SubWindowAction? = nil, resultingScreenFrame: CGRect? = nil
    ) {
        self.rect = rect
        self.screen = screen
        self.resultingAction = resultingAction
        self.resultingSubAction = resultingSubAction
        self.resultingScreenFrame = resultingScreenFrame
    }
}

struct RectResult {
    let rect: CGRect
    let subAction: SubWindowAction?
    let resultingAction: WindowAction?

    init(_ rect: CGRect, subAction: SubWindowAction? = nil, resultingAction: WindowAction? = nil) {
        self.rect = rect
        self.subAction = subAction
        self.resultingAction = resultingAction
    }
}

struct Window {
    let id: CGWindowID
    let rect: CGRect
}

// MARK: - Window Actions and Sub-actions
enum WindowAction: CaseIterable {
    case leftHalf, rightHalf, topHalf, bottomHalf
    case topLeft, topRight, bottomLeft, bottomRight
    case firstThird, centerThird, lastThird
    case firstTwoThirds, lastTwoThirds
    case maximize, almostMaximize, maximizeHeight
    case smaller, larger, center, centerProminently
    case nextDisplay, previousDisplay
    case restore
    case leftThird, rightThird
    case topLeftThird, topCenterThird, topRightThird
    case middleLeftThird, middleCenterThird, middleRightThird
    case bottomLeftThird, bottomCenterThird, bottomRightThird
    case leftTwoThirds, rightTwoThirds
    case topLeftTwoThirds, topRightTwoThirds
    case bottomLeftTwoThirds, bottomRightTwoThirds
    case specified
    case reverseAll, tileAll, cascadeAll, cascadeActiveApp
    case leftThird, rightThird, topThird, bottomThird
    case leftTwoThirds, rightTwoThirds, topTwoThirds, bottomTwoThirds
    case topLeftQuarter, topRightQuarter, bottomLeftQuarter, bottomRightQuarter
    case topLeftSixth, topCenterSixth, topRightSixth
    case bottomLeftSixth, bottomCenterSixth, bottomRightSixth
    case topLeftEighth, topCenterLeftEighth, topCenterRightEighth, topRightEighth
    case bottomLeftEighth, bottomCenterLeftEighth, bottomCenterRightEighth, bottomRightEighth
    case topLeftNinth, topCenterNinth, topRightNinth
    case middleLeftNinth, middleCenterNinth, middleRightNinth
    case bottomLeftNinth, bottomCenterNinth, bottomRightNinth
    case firstFourth, secondFourth, thirdFourth, lastFourth
    case firstThreeFourths, lastThreeFourths
    case moveLeft, moveRight, moveUp, moveDown
    case largerWidth, smallerWidth

    var resizes: Bool {
        switch self {
        case .moveLeft, .moveRight, .moveUp, .moveDown:
            return false
        default:
            return true
        }
    }
}

enum SubWindowAction {
    case leftHalf, rightHalf, topHalf, bottomHalf
    case topLeft, topRight, bottomLeft, bottomRight
    case leftThird, centerThird, rightThird
    case leftTwoThirds, rightTwoThirds
    case topThird, bottomThird
    case topTwoThirds, bottomTwoThirds
    case leftThreeFourths, rightThreeFourths
    case topThreeFourths, bottomThreeFourths
    case topLeftQuarter, topRightQuarter, bottomLeftQuarter, bottomRightQuarter
    case topLeftSixth, topCenterSixth, topRightSixth
    case bottomLeftSixth, bottomCenterSixth, bottomRightSixth
    case topLeftEighth, topCenterLeftEighth, topCenterRightEighth, topRightEighth
    case bottomLeftEighth, bottomCenterLeftEighth, bottomCenterRightEighth, bottomRightEighth
    case topLeftNinth, topCenterNinth, topRightNinth
    case middleLeftNinth, middleCenterNinth, middleRightNinth
    case bottomLeftNinth, bottomCenterNinth, bottomRightNinth
    case firstFourth, secondFourth, thirdFourth, lastFourth
    case firstThreeFourths, lastThreeFourths
}

// MARK: - WindowCalculationFactory
class WindowCalculationFactory {

    static let leftHalfCalculation = LeftHalfCalculation()
    static let rightHalfCalculation = RightHalfCalculation()
    static let topHalfCalculation = TopHalfCalculation()
    static let bottomHalfCalculation = BottomHalfCalculation()
    static let maximizeCalculation = MaximizeCalculation()
    static let centerCalculation = CenterCalculation()
    static let nextPrevDisplayCalculation = NextPrevDisplayCalculation()
    static let changeSizeCalculation = ChangeSizeCalculation()
    static let lowerLeftCalculation = LowerLeftCalculation()
    static let lowerRightCalculation = LowerRightCalculation()
    static let upperLeftCalculation = UpperLeftCalculation()
    static let upperRightCalculation = UpperRightCalculation()
    static let firstThirdCalculation = FirstThirdCalculation()
    static let centerThirdCalculation = CenterThirdCalculation()
    static let lastThirdCalculation = LastThirdCalculation()
    static let firstTwoThirdsCalculation = FirstTwoThirdsCalculation()
    static let lastTwoThirdsCalculation = LastTwoThirdsCalculation()
    static let firstThreeFourthsCalculation = FirstThreeFourthsCalculation()
    static let lastThreeFourthsCalculation = LastThreeFourthsCalculation()

    static let calculationsByAction: [WindowAction: WindowCalculation] = [
        .leftHalf: leftHalfCalculation,
        .rightHalf: rightHalfCalculation,
        .maximize: maximizeCalculation,
        .previousDisplay: nextPrevDisplayCalculation,
        .nextDisplay: nextPrevDisplayCalculation,
        .larger: changeSizeCalculation,
        .smaller: changeSizeCalculation,
        .largerWidth: changeSizeCalculation,
        .smallerWidth: changeSizeCalculation,
        .bottomHalf: bottomHalfCalculation,
        .topHalf: topHalfCalculation,
        .center: centerCalculation,
        .centerProminently: centerCalculation,
        .bottomLeft: lowerLeftCalculation,
        .bottomRight: lowerRightCalculation,
        .topLeft: upperLeftCalculation,
        .topRight: upperRightCalculation,
        .firstThird: firstThirdCalculation,
        .centerThird: centerThirdCalculation,
        .lastThird: lastThirdCalculation,
        .firstTwoThirds: firstTwoThirdsCalculation,
        .lastTwoThirds: lastTwoThirdsCalculation,
        .firstThreeFourths: firstThreeFourthsCalculation,
        .lastThreeFourths: lastThreeFourthsCalculation,
    ]
}

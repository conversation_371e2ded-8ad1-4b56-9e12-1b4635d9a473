//
//  LeftHalfCalculation.swift
//  Rectangle - Exact implementation from Rectangle
//
//  Created by <PERSON> on 6/13/19.
//  Copyright © 2019 <PERSON>. All rights reserved.
//

import Cocoa

class LeftHalfCalculation: WindowCalculation, RepeatedExecutionsInThirdsCalculation {

    override func calculateRect(_ params: RectCalculationParameters) -> RectResult {

        if params.lastAction == nil {
            return calculateFirstRect(params)
        }
        
        return calculateRepeatedRect(params)
    }
    
    func calculateFractionalRect(_ params: RectCalculationParameters, fraction: Float) -> RectResult {
        let visibleFrameOfScreen = params.visibleFrameOfScreen

        var rect = visibleFrameOfScreen
        rect.size.width = floor(visibleFrameOfScreen.width * CGFloat(fraction))
        return RectResult(rect)
    }
    
}

protocol RepeatedExecutionsInThirdsCalculation {
    func calculateFractionalRect(_ params: RectCalculationParameters, fraction: Float) -> RectResult
}

extension RepeatedExecutionsInThirdsCalculation {
    func calculateFirstRect(_ params: RectCalculationParameters) -> RectResult {
        return calculateFractionalRect(params, fraction: 0.5)
    }
    
    func calculateRepeatedRect(_ params: RectCalculationParameters) -> RectResult {
        return calculateFractionalRect(params, fraction: 2.0/3.0)
    }
}
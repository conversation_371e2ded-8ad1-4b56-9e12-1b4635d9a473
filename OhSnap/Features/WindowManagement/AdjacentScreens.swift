import AppKit
import Foundation

// Adapted from Rectangle (https://github.com/rxhanson/Rectangle) under GPL-3.0
// This matches <PERSON><PERSON>ang<PERSON>'s exact AdjacentScreens structure

struct AdjacentScreens {
    let prev: NSScreen
    let next: NSScreen
}

struct UsableScreens {
    let currentScreen: NSScreen
    let adjacentScreens: AdjacentScreens?
    let frameOfCurrentScreen: CGRect
    let numScreens: Int
    let screensOrdered: [NSScreen]

    init(
        currentScreen: NSScreen,
        adjacentScreens: AdjacentScreens? = nil,
        numScreens: Int,
        screensOrdered: [NSScreen]? = nil
    ) {
        self.currentScreen = currentScreen
        self.adjacentScreens = adjacentScreens
        self.frameOfCurrentScreen = currentScreen.frame
        self.numScreens = numScreens
        self.screensOrdered = screensOrdered ?? [currentScreen]
    }
}

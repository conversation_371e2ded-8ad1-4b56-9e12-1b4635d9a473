import AppKit
import Foundation

/// Calculation class for left and right half window positions with multi-display support
class LeftRightHalfCalculation: WindowCalculation {
    private let logger = LoggingService.shared
    private let serviceName = "LeftRightHalfCalculation"

    override func calculate(_ params: WindowCalculationParameters) -> WindowCalculationResult? {
        let usableScreens = params.usableScreens

        switch DefaultsManager.shared.subsequentExecutionMode {
        case .acrossMonitor:
            return calculateAcrossDisplays(params)

        case .acrossAndResize:
            if usableScreens.numScreens == 1 {
                return calculateResize(params)
            }
            return calculateAcrossDisplays(params)

        case .resize:
            return calculateResize(params)

        case .none, .cycleMonitor:
            let screen = usableScreens.currentScreen
            let oneHalfRect = calculateFirstRect(params.asRectParams())
            return WindowCalculationResult(
                rect: oneHalfRect.rect,
                screen: screen,
                resultingAction: params.action
            )
        }
    }

    /// Calculate window position when moving across displays
    private func calculateAcrossDisplays(_ params: WindowCalculationParameters)
        -> WindowCalculationResult?
    {
        let usableScreens = params.usableScreens
        let window = params.window
        let action = params.action

        // Get the next screen using Rectangle's approach
        let nextScreen = usableScreens.adjacentScreens?.next ?? usableScreens.currentScreen

        // Calculate the rect on the next screen
        let adjustedFrame = nextScreen.adjustedVisibleFrame(false, false)
        let rectParams = RectCalculationParameters(
            window: window,
            visibleFrameOfScreen: adjustedFrame,
            action: action,
            lastAction: nil
        )

        let rectResult = calculateFirstRect(rectParams)

        logger.debug("Moving window to next screen with action: \\(action)", service: serviceName)

        return WindowCalculationResult(
            rect: rectResult.rect,
            screen: nextScreen,
            resultingAction: action
        )
    }

    /// Calculate window position when resizing on the current screen
    private func calculateResize(_ params: WindowCalculationParameters) -> WindowCalculationResult?
    {
        let usableScreens = params.usableScreens
        let window = params.window
        let action = params.action
        let screen = usableScreens.currentScreen

        // Calculate the adjusted frame
        let adjustedFrame = screen.adjustedVisibleFrame(false, false)

        // Determine the new action based on the current action
        let newAction: WindowDirection

        switch action {
        case .leftHalf:
            newAction = .leftTwoThirds
        case .leftTwoThirds:
            newAction = .leftThird
        case .leftThird:
            newAction = .leftHalf
        case .rightHalf:
            newAction = .rightTwoThirds
        case .rightTwoThirds:
            newAction = .rightThird
        case .rightThird:
            newAction = .rightHalf
        default:
            newAction = action
        }

        // Calculate the rect with the new action
        let rectParams = RectCalculationParameters(
            window: window,
            visibleFrameOfScreen: adjustedFrame,
            action: newAction,
            lastAction: nil
        )

        let rectResult = calculateRect(rectParams)

        logger.debug("Resizing window with new action: \\(newAction)", service: serviceName)

        return WindowCalculationResult(
            rect: rectResult.rect,
            screen: screen,
            resultingAction: newAction
        )
    }

    /// Calculate the initial rectangle for the action
    private func calculateFirstRect(_ params: RectCalculationParameters) -> RectResult {
        let visibleFrameOfScreen = params.visibleFrameOfScreen

        switch params.action {
        case .leftHalf:
            return RectResult(
                CGRect(
                    x: visibleFrameOfScreen.minX,
                    y: visibleFrameOfScreen.minY,
                    width: visibleFrameOfScreen.width / 2,
                    height: visibleFrameOfScreen.height
                ))

        case .rightHalf:
            return RectResult(
                CGRect(
                    x: visibleFrameOfScreen.minX + visibleFrameOfScreen.width / 2,
                    y: visibleFrameOfScreen.minY,
                    width: visibleFrameOfScreen.width / 2,
                    height: visibleFrameOfScreen.height
                ))

        default:
            // For other actions, use the standard calculation
            return super.calculateRect(params)
        }
    }
}

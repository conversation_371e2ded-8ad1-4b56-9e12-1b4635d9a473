import AppKit
import Carbon

// Rectangle-compatible AccessibilityElement wrapper for screen detection
class RectangleAccessibilityElement {
    private let element: AXUIElement

    init(_ element: AXUIElement) {
        self.element = element
    }

    var frame: CGRect {
        // Get frame synchronously using direct AX calls
        var positionRef: CFTypeRef?
        var sizeRef: CFTypeRef?

        let positionError = AXUIElementCopyAttributeValue(
            element, kAXPositionAttribute as CFString, &positionRef)
        let sizeError = AXUIElementCopyAttributeValue(
            element, kAXSizeAttribute as CFString, &sizeRef)

        guard positionError == .success, sizeError == .success,
            let positionValue = positionRef, let sizeValue = sizeRef
        else {
            return CGRect.zero
        }

        var position = CGPoint.zero
        var size = CGSize.zero
        AXValueGetValue(positionValue as! AXValue, .cgPoint, &position)
        AXValueGetValue(sizeValue as! AXValue, .cgSize, &size)

        return CGRect(origin: position, size: size)
    }
}

// Adapted from Rectangle (https://github.com/rxhanson/Rectangle) under GPL-3.0
class WindowSnappingService {
    // MARK: - Constants
    private let serviceName = "WindowSnappingService"
    private let logger = LoggingService.shared
    private let screenDetection = ScreenDetectionService()

    // MARK: - Public Methods
    func snapFrontmostWindow(to position: SnapPosition) {
        logger.info("┌─── Rectangle Window Snapping ───┐", service: serviceName)
        logger.info("│ Snapping to position: \(position)", service: serviceName)

        // Get frontmost window using Rectangle's approach
        guard let frontmostWindowElement = AccessibilityElement.getFrontWindowElement() else {
            logger.error("│ Failed to get frontmost window", service: serviceName)
            logger.info("└─────────────────────────────────┘", service: serviceName)
            return
        }

        // Use Rectangle's exact screen detection
        guard let usableScreens = screenDetection.detectScreens(using: frontmostWindowElement)
        else {
            logger.error("│ Failed to detect screens", service: serviceName)
            logger.info("└─────────────────────────────────┘", service: serviceName)
            return
        }

        logger.debug("│ Detected \(usableScreens.numScreens) screens", service: serviceName)
        logger.debug("│ Current screen: \(usableScreens.currentScreen.frame)", service: serviceName)

        // Get current window frame and convert using Rectangle's coordinate system
        let currentWindowRect = frontmostWindowElement.frame
        let currentNormalizedRect = currentWindowRect.screenFlipped

        logger.debug("│ Window frame (AX): \(currentWindowRect)", service: serviceName)
        logger.debug("│ Window frame (normalized): \(currentNormalizedRect)", service: serviceName)

        // Calculate new position using Rectangle's approach
        guard let calculation = getCalculationForPosition(position) else {
            logger.error(
                "│ No calculation available for position: \(position)", service: serviceName)
            logger.info("└─────────────────────────────────┘", service: serviceName)
            return
        }

        let windowInfo = WindowInfo(
            frame: currentNormalizedRect,
            monitorID: nil,
            appBundleIdentifier: nil,
            isFullscreen: false
        )

        let params = RectCalculationParameters(
            window: windowInfo,
            visibleFrameOfScreen: usableScreens.currentScreen.adjustedVisibleFrame(),
            action: convertSnapPositionToWindowDirection(position),
            frameOfScreen: usableScreens.currentScreen.frame
        )

        let result = calculation.calculateRect(params)

        if result.rect.isNull {
            logger.error("│ Calculation returned null rect", service: serviceName)
            logger.info("└─────────────────────────────────┘", service: serviceName)
            return
        }

        // Convert back to AX coordinates and apply
        let newRect = result.rect.screenFlipped
        logger.debug("│ New rect (normalized): \(result.rect)", service: serviceName)
        logger.debug("│ New rect (AX): \(newRect)", service: serviceName)

        frontmostWindowElement.setFrame(newRect)

        logger.info(
            "│ Successfully snapped window using Rectangle's approach", service: serviceName)
        logger.info("└─────────────────────────────────┘", service: serviceName)
    }

    // MARK: - Helper Methods
    private func getCalculationForPosition(_ position: SnapPosition) -> WindowCalculation? {
        switch position {
        case .topHalf:
            return TopHalfCalculation()
        case .bottomHalf:
            return BottomHalfCalculation()
        case .leftHalf:
            return LeftRightHalfCalculation()
        case .rightHalf:
            return LeftRightHalfCalculation()
        case .fullscreen:
            return AlmostMaximizeCalculation()
        default:
            return StandardPositionCalculation()
        }
    }

    private func convertSnapPositionToWindowDirection(_ position: SnapPosition) -> WindowDirection {
        switch position {
        case .leftHalf: return .leftHalf
        case .rightHalf: return .rightHalf
        case .topHalf: return .topHalf
        case .bottomHalf: return .bottomHalf
        case .fullscreen: return .maximize
        case .leftThird: return .leftThird
        case .centerThird: return .centerThird
        case .rightThird: return .rightThird
        case .topLeftQuarter: return .topLeftQuarter
        case .topRightQuarter: return .topRightQuarter
        case .bottomLeftQuarter: return .bottomLeftQuarter
        case .bottomRightQuarter: return .bottomRightQuarter
        case .leftTwoThirds: return .leftTwoThirds
        case .rightTwoThirds: return .rightTwoThirds
        default: return .maximize
        }
    }
}

// MARK: - Error Types
enum WindowSnappingError: Error, CustomStringConvertible {
    case noFrontmostWindow
    case noTargetScreen

    var description: String {
        switch self {
        case .noFrontmostWindow:
            return "No frontmost window found"
        case .noTargetScreen:
            return "No target screen found"
        }
    }
}

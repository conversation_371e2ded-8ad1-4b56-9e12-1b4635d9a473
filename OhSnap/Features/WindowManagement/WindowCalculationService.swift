import AppKit
import CoreGraphics
import Foundation

// Rectangle's exact structs for calculations
struct RectangleWindow {
    let id: CGWindowID
    let rect: CGRect
}

struct RectangleWindowCalculationParameters {
    let window: RectangleWindow
    let usableScreens: UsableScreens
    let action: WindowDirection
    let lastAction: WindowDirection?
    let ignoreTodo: Bool

    func asRectParams(visibleFrame: CGRect? = nil, differentAction: WindowDirection? = nil)
        -> RectCalculationParameters
    {
        RectCalculationParameters(
            window: WindowInfo(
                frame: window.rect, monitorID: nil, appBundleIdentifier: nil, isFullscreen: false),
            visibleFrameOfScreen: visibleFrame
                ?? usableScreens.currentScreen.adjustedVisibleFrame(ignoreTodo, false),
            action: differentAction ?? action,
            frameOfScreen: usableScreens.currentScreen.frame,
            lastAction: lastAction
        )
    }
}

struct RectangleWindowCalculationResult {
    var rect: CGRect
    let screen: NSScreen
    let resultingAction: WindowDirection
    let resultingSubAction: WindowDirection?
    let resultingScreenFrame: CGRect?

    init(
        rect: CGRect,
        screen: NSScreen,
        resultingAction: WindowDirection,
        resultingSubAction: WindowDirection? = nil,
        resultingScreenFrame: CGRect? = nil
    ) {

        self.rect = rect
        self.screen = screen
        self.resultingAction = resultingAction
        self.resultingSubAction = resultingSubAction
        self.resultingScreenFrame = resultingScreenFrame
    }
}

class WindowCalculationService {
    private let screenDetection: ScreenDetectionService
    private let logger = LoggingService.shared
    private let serviceName = "WindowCalculationService"

    // Rectangle's exact calculation factory approach
    private let calculationsByAction: [WindowDirection: WindowCalculation] = [
        .leftHalf: LeftHalfCalculation(),
        .rightHalf: RightHalfCalculation(),
        .topHalf: TopHalfCalculation(),
        .bottomHalf: BottomHalfCalculation(),
        .maximize: MaximizeCalculation(),
        .centerTwoThirds: CenterTwoThirdsCalculation(),
        .almostMaximize: AlmostMaximizeCalculation(),
        .larger: ChangeSizeCalculation(),
        .smaller: ChangeSizeCalculation(),
        .largerWidth: ChangeSizeCalculation(),
        .smallerWidth: ChangeSizeCalculation(),
        .specified: SpecifiedCalculation(),
    ]

    init(screenDetection: ScreenDetectionService = ScreenDetectionService()) {
        self.screenDetection = screenDetection
        logger.info(
            "Initialized with Rectangle's exact calculation factory approach", service: serviceName)
    }

    func calculateWindowRect(
        for direction: WindowDirection,
        window: WindowInfo,
        screen: NSScreen,
        visibleFrameOnly: Bool = true,
        lastAction: WindowDirection? = nil
    ) -> CGRect {
        // Log screen information for debugging
        ScreenDebugger.shared.logScreenInfo()

        // Create UsableScreens structure like Rectangle does
        let allScreens = screenDetection.getAllScreens()
        let usableScreens = UsableScreens(
            currentScreen: screen,
            adjacentScreens: nil,  // Will be calculated if needed
            numScreens: allScreens.count,
            screensOrdered: allScreens
        )

        // Create the current window in Rectangle's format
        // Convert window frame to Rectangle's coordinate system (screenFlipped)
        let currentNormalizedRect = window.frame.screenFlipped
        // Use a dummy window ID since we don't have one in WindowInfo
        let currentWindow = RectangleWindow(id: 0, rect: currentNormalizedRect)

        // Create WindowCalculationParameters like Rectangle does
        let calculationParams = RectangleWindowCalculationParameters(
            window: currentWindow,
            usableScreens: usableScreens,
            action: direction,
            lastAction: lastAction,
            ignoreTodo: false
        )

        // Get the calculation from Rectangle's factory approach
        guard let windowCalculation = calculationsByAction[direction] else {
            logger.error("No calculation found for direction: \(direction)", service: serviceName)
            return window.frame  // Return original frame as fallback
        }

        // Execute Rectangle's exact calculation pipeline
        guard let calcResult = windowCalculation.calculate(calculationParams) else {
            logger.error(
                "Calculation returned nil result for direction: \(direction)", service: serviceName)
            return window.frame  // Return original frame as fallback
        }

        logger.debug("Rectangle calculation result: \(calcResult.rect)", service: serviceName)

        // Rectangle's final step: flip coordinates back for AX system
        let finalRect = calcResult.rect.screenFlipped

        logger.debug("Final rect after screenFlipped: \(finalRect)", service: serviceName)
        return finalRect
    }

    /// Helper to determine if screens are arranged vertically
    private func isVerticalScreenArrangement(screens: [NSScreen]) -> Bool {
        guard screens.count > 1 else { return false }

        // Calculate the total width and height of the arrangement
        var minX: CGFloat = .infinity
        var maxX: CGFloat = -.infinity
        var minY: CGFloat = .infinity
        var maxY: CGFloat = -.infinity

        for screen in screens {
            minX = min(minX, screen.frame.minX)
            maxX = max(maxX, screen.frame.maxX)
            minY = min(minY, screen.frame.minY)
            maxY = max(maxY, screen.frame.maxY)
        }

        let totalWidth = maxX - minX
        let totalHeight = maxY - minY

        // Check if any screen is positioned below another screen
        var hasVerticalStacking = false

        // For macOS, we need to be more careful about detecting vertical stacking
        // because the coordinate system has (0,0) at the bottom-left
        if screens.count == 2 {
            let screen1 = screens[0]
            let screen2 = screens[1]

            // Calculate the vertical overlap percentage
            let verticalOverlap =
                min(screen1.frame.maxY, screen2.frame.maxY)
                - max(screen1.frame.minY, screen2.frame.minY)
            let minHeight = min(screen1.frame.height, screen2.frame.height)
            let verticalOverlapPercentage = verticalOverlap / minHeight

            // Calculate the horizontal overlap percentage
            let horizontalOverlap =
                min(screen1.frame.maxX, screen2.frame.maxX)
                - max(screen1.frame.minX, screen2.frame.minX)
            let minWidth = min(screen1.frame.width, screen2.frame.width)
            let horizontalOverlapPercentage = horizontalOverlap / minWidth

            // If there's significant horizontal overlap and minimal vertical overlap,
            // it's likely a vertical arrangement
            hasVerticalStacking =
                horizontalOverlapPercentage > 0.5 && verticalOverlapPercentage < 0.2

            // Also check if one screen is completely above the other
            if !hasVerticalStacking {
                hasVerticalStacking =
                    (screen1.frame.minY >= screen2.frame.maxY)
                    || (screen2.frame.minY >= screen1.frame.maxY)
            }
        } else {
            // For more than 2 screens, use the original approach
            for i in 0..<screens.count {
                for j in (i + 1)..<screens.count {
                    let screen1 = screens[i]
                    let screen2 = screens[j]

                    // Check if one screen is below the other
                    if (screen1.frame.minY >= screen2.frame.maxY)
                        || (screen2.frame.minY >= screen1.frame.maxY)
                    {
                        hasVerticalStacking = true
                        break
                    }
                }
                if hasVerticalStacking { break }
            }
        }

        // Consider it vertical if either:
        // 1. The total height is significantly larger than width
        // 2. We detected screens stacked on top of each other
        let isVertical = (totalHeight > totalWidth * 1.2) || hasVerticalStacking

        logger.debug("isVerticalScreenArrangement result: \(isVertical)", service: serviceName)
        logger.debug(
            "  Total width: \(totalWidth), total height: \(totalHeight)", service: serviceName)
        logger.debug("  Has vertical stacking: \(hasVerticalStacking)", service: serviceName)

        return isVertical
    }
}

import AppKit
import Carbon

// Rectangle-compatible AccessibilityElement wrapper for screen detection
class RectangleAccessibilityElement {
    private let element: AXUIElement

    init(_ element: AXUIElement) {
        self.element = element
    }

    var frame: CGRect {
        // Get frame synchronously using direct AX calls
        var positionRef: CFTypeRef?
        var sizeRef: CFTypeRef?

        let positionError = AXUIElementCopyAttributeValue(
            element, kAXPositionAttribute as CFString, &positionRef)
        let sizeError = AXUIElementCopyAttributeValue(
            element, kAXSizeAttribute as CFString, &sizeRef)

        guard positionError == .success, sizeError == .success,
            let positionValue = positionRef, let sizeValue = sizeRef
        else {
            return CGRect.zero
        }

        var position = CGPoint.zero
        var size = CGSize.zero
        AXValueGetValue(positionValue as! AXValue, .cgPoint, &position)
        AXValueGetValue(sizeValue as! AXValue, .cgSize, &size)

        // Convert from AX coordinates to Cocoa coordinates to match <PERSON><PERSON><PERSON><PERSON>'s expectations
        // AX coordinates have (0,0) at top-left, Cocoa has (0,0) at bottom-left
        // Rectangle's screenContaining method expects Cocoa coordinates
        if let screen = NSScreen.screens.first(where: {
            // Check which screen contains this position in AX coordinates
            let axFrame = CGRect(origin: position, size: size)
            return $0.frame.intersects(axFrame) || $0.frame.contains(position)
        }) {
            // Convert Y coordinate from AX to Cocoa
            position.y = screen.frame.height - (position.y + size.height)
        }

        return CGRect(origin: position, size: size)
    }
}

// Adapted from Rectangle (https://github.com/rxhanson/Rectangle) under GPL-3.0
class WindowSnappingService {
    // MARK: - Constants
    private let serviceName = "WindowSnappingService"
    private let logger = LoggingService.shared

    // MARK: - Dependencies
    private let windowMover: WindowMover
    private let calculationService: WindowCalculationService
    private let screenDetection: ScreenDetectionService
    private let accessibilityElement: AccessibilityElement

    // MARK: - Initialization
    init(
        windowMover: WindowMover = WindowMover(),
        calculationService: WindowCalculationService = WindowCalculationService(),
        screenDetection: ScreenDetectionService = ScreenDetectionService(),
        accessibilityElement: AccessibilityElement = AccessibilityElement()
    ) {
        self.windowMover = windowMover
        self.calculationService = calculationService
        self.screenDetection = screenDetection
        self.accessibilityElement = accessibilityElement
        logger.info("Initialized with dependencies", service: serviceName)
    }

    // MARK: - Public Methods
    func snapFrontmostWindow(to position: SnapPosition) {
        Task {
            do {
                let window = try await getFrontmostWindow()
                try await snapWindow(window, to: position)
            } catch WindowSnappingError.noFrontmostWindow {
                logger.warning("No frontmost window found", service: serviceName)
            } catch WindowSnappingError.noTargetScreen {
                logger.warning("Failed to determine target screen", service: serviceName)
            } catch {
                logger.error("Error snapping window: \(error)", service: serviceName)
            }
        }
    }

    // MARK: - Private Methods
    private func snapWindow(_ window: AXUIElement?, to position: SnapPosition) async throws {
        guard let window = window else {
            logger.warning("Window is nil", service: serviceName)
            throw WindowSnappingError.noFrontmostWindow
        }

        // Log screen information for debugging
        ScreenDebugger.shared.logScreenInfo()

        // Get current window info for screen detection
        let windowInfo = try await accessibilityElement.windowInfo(for: window)

        logger.debug("┌─── Window Snapping Debug ───┐", service: serviceName)
        logger.debug("│ Snapping to position: \(position)", service: serviceName)
        logger.debug("│ Window info: \(windowInfo)", service: serviceName)

        // Create a Rectangle-style AccessibilityElement wrapper for screen detection
        let frontmostWindowElement = RectangleAccessibilityElement(window)

        // Use Rectangle's exact screen detection approach
        guard let usableScreens = screenDetection.detectScreens(using: frontmostWindowElement)
        else {
            logger.warning(
                "│ Failed to detect screens using Rectangle's method", service: serviceName)
            logger.debug("└─────────────────────────────────────┘", service: serviceName)
            throw WindowSnappingError.noTargetScreen
        }

        let targetScreen = usableScreens.currentScreen

        // Log target screen information
        logger.debug("│ Target screen frame: \(targetScreen.frame)", service: serviceName)
        logger.debug(
            "│ Target screen visible frame: \(targetScreen.visibleFrame)", service: serviceName)
        logger.debug("│ Number of screens: \(usableScreens.numScreens)", service: serviceName)

        if let screenNumber = targetScreen.deviceDescription[
            NSDeviceDescriptionKey("NSScreenNumber")] as? NSNumber
        {
            logger.debug("Target screen number: \(screenNumber)", service: serviceName)
        }

        if targetScreen == NSScreen.main {
            logger.debug("Target screen is main screen", service: serviceName)
        } else {
            logger.debug("Target screen is secondary screen", service: serviceName)
        }

        // Convert SnapPosition to WindowDirection and move window
        let direction = convertSnapPositionToDirection(position)
        logger.debug("│ Converted to direction: \(direction)", service: serviceName)
        logger.debug("└─────────────────────────────────────┘", service: serviceName)

        try await windowMover.moveWindow(window, to: direction, on: targetScreen)

        logger.info("Successfully snapped window to \(position)", service: serviceName)
    }

    private func getFrontmostWindow() async throws -> AXUIElement? {
        guard let frontmostApp = NSWorkspace.shared.frontmostApplication else {
            logger.warning("No frontmost application found", service: serviceName)
            return nil
        }

        let appElement = AXUIElementCreateApplication(frontmostApp.processIdentifier)
        var value: AnyObject?

        let error = AXUIElementCopyAttributeValue(
            appElement,
            kAXFocusedWindowAttribute as CFString,
            &value
        )

        guard error == .success else {
            logger.error("Failed to get focused window: \(error)", service: serviceName)
            throw AccessibilityError.failedToGetAttribute(error)
        }

        return (value as! AXUIElement)
    }

    // MARK: - Internal Methods
    internal func convertSnapPositionToDirection(_ position: SnapPosition) -> WindowDirection {
        logger.debug("Converting position \(position) to direction", service: serviceName)
        switch position {
        case .leftHalf: return .leftHalf
        case .rightHalf: return .rightHalf
        case .topHalf: return .topHalf
        case .bottomHalf: return .bottomHalf
        case .fullscreen: return .maximize
        case .leftThird: return .leftThird
        case .centerThird: return .centerThird
        case .rightThird: return .rightThird
        case .topLeftQuarter: return .topLeftQuarter
        case .topRightQuarter: return .topRightQuarter
        case .bottomLeftQuarter: return .bottomLeftQuarter
        case .bottomRightQuarter: return .bottomRightQuarter
        case .leftTwoThirds: return .leftTwoThirds
        case .centerTwoThirds: return .centerTwoThirds
        case .rightTwoThirds: return .rightTwoThirds
        case .custom(let rect): return .custom(rect)
        }
    }
}

// MARK: - Error Types
enum WindowSnappingError: Error, CustomStringConvertible {
    case noFrontmostWindow
    case noTargetScreen

    var description: String {
        switch self {
        case .noFrontmostWindow:
            return "No frontmost window found"
        case .noTargetScreen:
            return "No target screen found"
        }
    }
}
